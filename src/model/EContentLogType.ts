type IEContentLogType = {
  id: number;
  name: string;
  value: string;
};

export const ManualLog: IEContentLogType = {
  id: 1,
  name: 'Manual Log',
  value: 'Manual Log',
};

export const StatusChange: IEContentLogType = {
  id: 2,
  name: 'Status Change',
  value: 'Status Change',
};

export const TextToAudio: IEContentLogType = {
  id: 3,
  name: 'Text to Audio',
  value: 'Text to Audio',
};

const Basic = [ManualLog, StatusChange, TextToAudio];

const BasicByValue = Basic.reduce((hash, status) => {
  hash[status.value] = status;
  return hash;
}, {});

const ById = Basic.reduce((hash, type) => {
  hash[type.id] = type;
  return hash;
}, {});

const BasicProps = Object.keys(BasicByValue);

export default {
  Basic,
  ById,
  BasicByValue,
  BasicProps,
  ManualLog,
  StatusChange,
  TextToAudio,
};

export type TEContentLogType = 'Manual Log' | 'Status Change' | 'Text to Audio';
