import {
  Form,
  FormikValues,
  FormikProps,
  FormikState,
  FormikTouched,
} from 'formik';
import { isEmpty, isEqual } from 'lodash';
import React, {
  PropsWithChildren,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { Location } from 'history';
import { useHistory } from 'react-router-dom';
import { useDebounce } from 'react-use';
import classNames from 'classnames';

import useT from '../../utils/Translations/useT';
import { IBasicEntity, IOnLeaveParams } from './EntityForm';
import EntityFormPrompt from './EntityFormPrompt';
import { DEFAULT_WRAPPER_CLASS_NAMES } from './internal/EntityFieldContent';
import EntityFormContent from './internal/EntityFormContent';
import SubmitSection, { ISubmitSectionAction } from './internal/SubmitSection';
import RoundedPrimaryButton from '../../controls/RoundedPrimaryButton';
import Spinner from '../../utils/Spinner';
import styles from './EntityFormBody.scss';
import { useIsOnline } from '../../utils/InternetConnectionProvider';
import useEntityFormContext from './internal/useEntityFormContext';

const DELAY = 50;
const DEBOUNCE_TIME = 300;
const SCROLL_OFFSET = 150;

export default function EntityFormBody<Values extends IBasicEntity>({
  leftBottomSection,
  isSubmitTogglesDropdown,
  isSubmitDisabled,
  isCancelDisabled,
  hasClearFormButton,
  goBackLabel,
  defaultEntity,
  submitActions,
  submitSectionRenderer,
  isNew,
  promptMessage,
  hasLeavePrompt,
  updateLabel,
  deleteLabel,
  children,
  onDelete,
  onGoBack,
  hasResetOnCancel,
  onCancel,
  resetForm,
  dirty,
  values,
  isSubmitting,
  entity,
  columns,
  entityName,
  createLabel,
  cancelLabel,
  fieldWrapperClassNames,
  isEditing,
  hasErrors,
  onLeave,
  hasSubmitOnChange,
  pullLeft,
  submitContainerClass,
  submitClass,
  isValid,
  submitForm,
  isCopied,
  touched,
  floatButton,
  submitBtnAdditionClasses,
}: Readonly<PropsWithChildren<IEntityFormBodyProps<Values>>>) {
  const t = useT();
  const history = useHistory();
  const formRef = useRef<HTMLFormElement>(null);

  const resetOnCancel = useCallback(() => {
    hasResetOnCancel && resetForm();
    onCancel && onCancel();
  }, [onCancel, hasResetOnCancel, resetForm]);

  const _isDirty = useMemo(() => {
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    // @ts-ignore
    const { gptVersion, replicateVersion, inputAuto, ..._values } = values;
    return dirty && !isEqual(_values, entity);
  }, [values, dirty, entity]);

  const _promptActive = useMemo<boolean>(() => {
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    // @ts-ignore
    const { cookedClobs, ...rest } = touched;
    return dirty && !isSubmitting && !isEmpty(rest);
  }, [dirty, isSubmitting, touched]);

  const _promptMessage = useMemo<string>(
    () =>
      promptMessage ||
      t(
        'The changes have not been saved yet, you need to click on the #{button} button in order to save changes',
        { button: isNew ? createLabel : updateLabel },
      ),
    [t, promptMessage, isNew, createLabel, updateLabel],
  );

  useEffect(() => {
    if (!onLeave || !isEditing) {
      return undefined;
    }

    const release = history.block((location: Location) => {
      (async () => {
        await onLeave(values, { isDirty: _isDirty, location, isValid });
      })();
      release();
      history.push(location);
    });

    return () => {
      release();
    };
  }, [_isDirty, isEditing, onLeave, history, values, isValid]);

  useDebounce(
    async () => {
      const valuesEqualInitialValues = isEqual(values, entity);

      if (
        hasSubmitOnChange &&
        !valuesEqualInitialValues &&
        isValid &&
        _isDirty
      ) {
        await submitForm();
      }
    },
    DEBOUNCE_TIME,
    [hasSubmitOnChange, values, entity, isValid, _isDirty, submitForm],
  );

  // eslint-disable-next-line no-nested-ternary
  const label = isSubmitting ? <Spinner /> : isNew ? createLabel : updateLabel;
  const { isOnline } = useIsOnline();
  const formikBag = useEntityFormContext();

  const _isSubmitDisabled = useMemo(() => {
    if (!isOnline) {
      return true;
    }

    const basicIsDisabled = isSubmitting || (!isNew && !_isDirty);

    return isSubmitDisabled
      ? isSubmitDisabled(isSubmitting, { ...formikBag, dirty: _isDirty })
      : basicIsDisabled;
  }, [isNew, isOnline, isSubmitting, formikBag, isSubmitDisabled, _isDirty]);

  const [scrollTop, setScrollTop] = useState(0);
  useEffect(() => {
    const scrollElement = document.getElementById('scroll-layout');

    const handleScroll = () => {
      if (scrollElement) {
        const scrollBottom =
          scrollElement.scrollHeight -
          scrollElement.scrollTop -
          scrollElement.clientHeight -
          SCROLL_OFFSET;
        setScrollTop(scrollBottom < 0 ? 0 : scrollBottom);
      }
    };

    if (scrollElement) {
      scrollElement.addEventListener('scroll', handleScroll);
    }
    handleScroll();

    return () => {
      if (scrollElement) {
        scrollElement.removeEventListener('scroll', handleScroll);
        handleScroll();
      }
    };
  }, []);

  const elementRef = useRef(null);
  const [isVisible, setIsVisible] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const timeoutRef = useRef(null); // To clear previous timeouts

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
        // @ts-ignore
        if (timeoutRef.current) clearTimeout(timeoutRef.current);

        setIsTransitioning(true);

        // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
        // @ts-ignore
        timeoutRef.current = setTimeout(() => {
          setIsVisible(entry.isIntersecting);
          setIsTransitioning(false);
        }, DELAY);
      },
      {
        threshold: 0.1, // Require 10% visibility to trigger
      },
    );

    const currentElement = elementRef.current;
    if (currentElement) {
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      // @ts-ignore
      observer.observe(currentElement);
    }

    return () => {
      if (currentElement) {
        // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
        // @ts-ignore
        observer.unobserve(currentElement);
      }
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      // @ts-ignore
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
    };
  }, []);

  const submitSection = useMemo(
    () =>
      submitSectionRenderer ? (
        submitSectionRenderer({
          isEditing,
          isNew,
          onDelete,
          onGoBack,
          isDirty: _isDirty,
          isSubmitting,
          resetForm,
          hasErrors,
        })
      ) : (
        <>
          <div ref={elementRef}>
            <SubmitSection<Values>
              actions={submitActions}
              cancelLabel={cancelLabel}
              createLabel={createLabel}
              defaultEntity={defaultEntity}
              deleteLabel={deleteLabel}
              goBackLabel={goBackLabel}
              hasClearFormButton={hasClearFormButton}
              hasErrors={hasErrors}
              isCancelDisabled={isCancelDisabled}
              isDirty={isCopied !== undefined ? isCopied : _isDirty}
              isEditing={isEditing}
              isNew={isNew}
              isSubmitDisabled={isSubmitDisabled}
              isSubmitting={isSubmitting}
              isSubmitTogglesDropdown={isSubmitTogglesDropdown}
              leftBottomSection={leftBottomSection}
              pullLeft={pullLeft}
              submitBtnAdditionClasses={submitBtnAdditionClasses}
              submitClass={submitClass}
              submitContainerClass={submitContainerClass}
              updateLabel={updateLabel}
              onCancel={
                onCancel || hasResetOnCancel ? resetOnCancel : undefined
              }
              onDelete={onDelete}
              onGoBack={onGoBack}
            />
          </div>
          <RoundedPrimaryButton
            additionClasses={classNames(
              'btn btn-primary legitRipple',
              // Show floating button only when footer is not visible and not transitioning
              floatButton &&
                !isVisible &&
                !isTransitioning &&
                !isNew &&
                isEditing
                ? styles.stickyButton
                : styles.hiddenButton,
            )}
            disabled={_isSubmitDisabled || !_isDirty}
            id="button-submit"
            // eslint-disable-next-line react/forbid-component-props
            style={{
              bottom: `${scrollTop}px`,
            }}
            type="submit"
          >
            {label}
          </RoundedPrimaryButton>
        </>
      ),
    [
      deleteLabel,
      scrollTop,
      updateLabel,
      submitSectionRenderer,
      submitActions,
      resetOnCancel,
      resetForm,
      onDelete,
      onGoBack,
      onCancel,
      isSubmitting,
      leftBottomSection,
      isSubmitTogglesDropdown,
      isSubmitDisabled,
      isEditing,
      isNew,
      hasResetOnCancel,
      hasClearFormButton,
      goBackLabel,
      defaultEntity,
      createLabel,
      cancelLabel,
      _isDirty,
      hasErrors,
      floatButton,
      _isSubmitDisabled,
      label,
      isCancelDisabled,
      isCopied,
      pullLeft,
      submitContainerClass,
      submitClass,
      isVisible,
      isTransitioning,
      submitBtnAdditionClasses,
    ],
  );

  return (
    <EntityFormContent
      columns={columns}
      createLabel={createLabel}
      entityName={entityName}
      fieldWrapperClassNames={fieldWrapperClassNames}
      isEditing={isEditing}
      isNew={isNew}
      updateLabel={updateLabel}
    >
      <Form
        ref={formRef}
        className={classNames('clearfix', {
          [styles.floatButtonForm]: floatButton,
        })}
        method="POST"
      >
        {hasLeavePrompt ? (
          <EntityFormPrompt message={_promptMessage} when={_promptActive} />
        ) : null}

        {children}
        {submitSection}
      </Form>
    </EntityFormContent>
  );
}

interface IEntityFormBodyProps<Values extends IBasicEntity> {
  hasResetOnCancel?: boolean;
  onGoBack?: () => void;
  onDelete?: () => void;
  onCancel?: () => void;
  resetForm: (nextState?: Partial<FormikState<Values>>) => void;
  dirty: boolean;
  touched: FormikTouched<Values>;
  values: Values;
  isSubmitting: boolean;
  hasErrors: boolean;
  entity: Values;
  columns?: number;
  createLabel: string;
  cancelLabel?: string;
  deleteLabel?: string;
  fieldWrapperClassNames?: Partial<typeof DEFAULT_WRAPPER_CLASS_NAMES>;
  isEditing: boolean;
  isCancelDisabled?: boolean;
  updateLabel: string;
  hasLeavePrompt?: boolean;
  isSubmitTogglesDropdown?: boolean;
  isNew: boolean;
  submitSectionRenderer?: TSubmitSectionRenderer;
  submitActions?: ISubmitSectionAction<Values>[];
  defaultEntity: {};
  goBackLabel: string;
  hasClearFormButton: boolean | ((formikBag: FormikValues) => boolean);
  isSubmitDisabled?: (
    isDisabled: boolean,
    formikBag: FormikProps<Values>,
  ) => boolean;
  entityName?: string;
  promptMessage?: string;
  leftBottomSection?: JSX.Element;
  onLeave?: (values: Values, params: IOnLeaveParams) => void;
  hasSubmitOnChange?: boolean;
  pullLeft?: boolean;
  submitContainerClass?: string;
  submitClass?: string;
  isValid?: boolean;
  submitForm: () => Promise<void>;
  isCopied?: boolean;
  floatButton?: boolean;
  submitBtnAdditionClasses?: string;
}

export type TSubmitSectionRenderer = (options: {
  isEditing: boolean;
  isNew: boolean;
  onDelete?: () => void;
  onGoBack?: () => void;
  isDirty: boolean;
  isSubmitting: boolean;
  resetForm: () => void;
  hasErrors: boolean;
}) => JSX.Element | null;
