import React, { useCallback } from 'react';
import { get, uniq } from 'lodash';

import TableCellTooltip from '../../../../common/components/utils/TableCellToolTip';
import useT from '../../../../common/components/utils/Translations/useT';
import ILanguage from '../../../../common/abstract/ILanguage';

const MAX_VISIBLE_ITEMS = 2;

export interface ILanguageCell {
  node: { languages: ILanguage[] };
}

const LanguageCell: React.FC<ILanguageCell> = ({ node }) => {
  const t = useT();
  const languages = uniq(get(node, 'languages', []));

  const renderTooltipLine = useCallback(({ name }) => <p>{t(name)}</p>, [t]);

  const renderTitle = useCallback(() => {
    const title = [t(languages[0]?.name)];

    if (languages.length > 1) {
      let i = 1;
      while (languages[i] && i < MAX_VISIBLE_ITEMS) {
        title.push(', ');
        title.push(t(languages[i]?.name));
        i++;
      }
    }

    if (languages.length > MAX_VISIBLE_ITEMS) {
      title.push('...');
    }
    return title;
  }, [t, languages]);

  return (
    <span>
      {languages ? (
        <span>
          {renderTitle()}
          <TableCellTooltip lines={languages} minLineLength={2} trigger="hover">
            {renderTooltipLine}
          </TableCellTooltip>
        </span>
      ) : (
        ''
      )}
    </span>
  );
};

export default LanguageCell;
