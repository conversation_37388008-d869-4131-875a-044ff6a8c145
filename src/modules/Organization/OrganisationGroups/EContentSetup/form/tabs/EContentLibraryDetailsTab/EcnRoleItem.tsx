import React, { useCallback } from 'react';
import { Draggable } from 'react-beautiful-dnd';
import classnames from 'classnames';

import styles from './EcnRoleItem.scss';
import Ripple from '../../../../../../../common/components/utils/Ripple';
import Icon from '../../../../../../../common/components/utils/Icon';
import TextField from '../../../../../../../common/components/containers/EntityForm/fields/TextField';
import useT from '../../../../../../../common/components/utils/Translations/useT';
import StatusField from '../../../../../../../common/components/containers/EntityForm/fields/StatusField';
import { IEcnRoleItem } from '../../../../../../../common/abstract/EContent/IEContentLibrary';
import Statuses from '../../../../../../../model/Statuses';
import useEntityFormContext from '../../../../../../../common/components/containers/EntityForm/internal/useEntityFormContext';
import EntityField from '../../../../../../../common/components/containers/EntityForm/internal/EntityField';
import FormTextField from '../../../../../../../common/components/controls/FormTextField';

const EcnRoleItem: React.FC<{
  item: IEcnRoleItem;
  index: number;
  isDraggable?: boolean;
  wrapClassName?: string;
  lastItem?: boolean;
  errorMessage?: any;
}> = ({ index, item, isDraggable, wrapClassName, lastItem, errorMessage }) => {
  const t = useT();
  const { setFieldValue, values } = useEntityFormContext();

  const handleNameChange = useCallback(
    fieldOnChange => event => {
      // Call the original field onChange first to update the form state
      fieldOnChange(event);

      const value = event.target.value;
      const currentStatus = values.ecnRoles?.[index]?.status;

      // If user starts typing and status is null/empty, set it to active
      if (
        value &&
        (!currentStatus || currentStatus === null || currentStatus === '')
      ) {
        setFieldValue(`ecnRoles[${index}].status`, Statuses.Active.value);
      }
    },
    [setFieldValue, values.ecnRoles, index],
  );

  const renderLink = useCallback(
    () => (
      <Ripple childRefProp="innerRef">
        <>
          <EntityField<string>
            columns={4}
            name={`ecnRoles[${index}].name`}
            required={!index || !lastItem}
          >
            {(editable, field, { disabled, required }) =>
              editable ? (
                <FormTextField
                  animateTitle={false}
                  disabled={disabled}
                  label={t('Role')}
                  maxLength={70}
                  noLabel
                  placeholder={t('Role')}
                  required={required}
                  {...field}
                  input={{
                    ...field.input,
                    onChange: handleNameChange(field.input.onChange),
                  }}
                  errorMessage={errorMessage}
                />
              ) : (
                <div>{field.input.value}</div>
              )
            }
          </EntityField>
          <StatusField
            columns={4}
            hasLabel={false}
            name={`ecnRoles[${index}].status`}
            required={!index || !lastItem}
            withTitle={false}
          />
        </>
      </Ripple>
    ),
    [t, index, lastItem, errorMessage, handleNameChange],
  );

  const renderDraggableItem = useCallback(
    () => (
      <Draggable
        key={`role-${index}`}
        draggableId={(index as number).toString()}
        index={index}
        isDragDisabled={lastItem}
      >
        {provided => (
          <div
            key={index}
            ref={provided.innerRef}
            {...provided.draggableProps}
            {...provided.dragHandleProps}
            className={classnames(styles.linkWrap, wrapClassName)}
            // eslint-disable-next-line react/forbid-dom-props
            style={{ ...provided.draggableProps.style }}
          >
            <Icon
              badge="drag_indicator"
              className={classnames(styles.linkDragIcon, {
                [styles.opacityZero]: lastItem,
              })}
              name="material-icons"
              type="material"
            />
            {renderLink()}
          </div>
        )}
      </Draggable>
    ),
    [index, renderLink, wrapClassName, lastItem],
  );

  return isDraggable ? renderDraggableItem() : renderLink();
};

export default EcnRoleItem;
